import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { Settings, Database, Download, Palette, Globe, RefreshCw, Trash2, Eye, Type, Zap } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ImportExportControls, ThemeToggle, DatabaseSchema, useAccessibility } from '../../components/shared';
import { useAppStore } from '../../store';
import { useDatabase } from '../../lib/useDatabase';

export default function SettingsPage() {
  const { t } = useTranslation();
  const { currentLanguage, setCurrentLanguage, swimsuits, girls, skills } = useAppStore();
  const { populateWithSampleData, loadDataFromDatabase } = useDatabase();
  const { reducedMotion, highContrast, fontSize, announceMessage } = useAccessibility();

  const languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'ja', name: '日本語', flag: '🇯🇵' },
    { code: 'zh', name: '简体中文', flag: '🇨🇳' },
  ];

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center mb-8"
      >
        <div className="flex items-center justify-center space-x-3 mb-4">
          <Settings className="w-8 h-8 text-accent-gold" />
          <h1 className="text-3xl font-bold text-foreground">
            {t('nav.settings')}
          </h1>
        </div>
        <p className="text-muted-foreground">
          Manage your preferences and data
        </p>
      </motion.div>

      <div className="space-y-8">
        {/* Accessibility Settings */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="enhanced-card">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Eye className="w-5 h-5 text-accent-cyan" />
                <span>Accessibility</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Font Size */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    <Type className="w-4 h-4 inline mr-2" />
                    Font Size
                  </label>
                  <Select
                    value={fontSize}
                    onValueChange={(value: 'normal' | 'large' | 'extra-large') => {
                      const root = document.documentElement;
                      root.classList.remove('font-large', 'font-extra-large');
                      if (value === 'large') root.classList.add('font-large');
                      if (value === 'extra-large') root.classList.add('font-extra-large');
                      localStorage.setItem('doaxvv-font-size', value);
                      announceMessage(`Font size changed to ${value}`);
                    }}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select font size" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="normal">Normal (16px)</SelectItem>
                      <SelectItem value="large">Large (18px)</SelectItem>
                      <SelectItem value="extra-large">Extra Large (20px)</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground mt-1">
                    Adjust text size for better readability
                  </p>
                </div>

                {/* Motion Preferences */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    <Zap className="w-4 h-4 inline mr-2" />
                    Motion Preferences
                  </label>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-3 rounded-lg border border-border bg-muted/30">
                      <div>
                        <div className="text-sm font-medium">Reduced Motion</div>
                        <div className="text-xs text-muted-foreground">
                          {reducedMotion ? 'Enabled by system' : 'Disabled'}
                        </div>
                      </div>
                      <div className={`w-3 h-3 rounded-full ${reducedMotion ? 'bg-accent-cyan' : 'bg-muted-foreground'}`} />
                    </div>
                    <div className="flex items-center justify-between p-3 rounded-lg border border-border bg-muted/30">
                      <div>
                        <div className="text-sm font-medium">High Contrast</div>
                        <div className="text-xs text-muted-foreground">
                          {highContrast ? 'Enabled by system' : 'Disabled'}
                        </div>
                      </div>
                      <div className={`w-3 h-3 rounded-full ${highContrast ? 'bg-accent-cyan' : 'bg-muted-foreground'}`} />
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">
                    These settings are automatically detected from your system preferences
                  </p>
                </div>
              </div>

              {/* Accessibility Features Info */}
              <div className="border-t border-border pt-4">
                <h4 className="text-sm font-medium text-foreground mb-3">Accessibility Features</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-xs text-muted-foreground">
                  <div className="flex items-start space-x-2">
                    <div className="w-2 h-2 rounded-full bg-accent-cyan mt-1.5 flex-shrink-0" />
                    <span>WCAG AA compliant color contrast ratios (4.5:1 minimum)</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <div className="w-2 h-2 rounded-full bg-accent-cyan mt-1.5 flex-shrink-0" />
                    <span>Minimum 44px touch targets for mobile accessibility</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <div className="w-2 h-2 rounded-full bg-accent-cyan mt-1.5 flex-shrink-0" />
                    <span>Keyboard navigation support with visible focus indicators</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <div className="w-2 h-2 rounded-full bg-accent-cyan mt-1.5 flex-shrink-0" />
                    <span>Screen reader announcements for important actions</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <div className="w-2 h-2 rounded-full bg-accent-cyan mt-1.5 flex-shrink-0" />
                    <span>Respects system preferences for reduced motion</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <div className="w-2 h-2 rounded-full bg-accent-cyan mt-1.5 flex-shrink-0" />
                    <span>High contrast mode support for better visibility</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Theme Settings */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="enhanced-card">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Palette className="w-5 h-5 text-accent-purple" />
                <span>{t('settings.appearance')}</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-foreground">{t('settings.theme')}</h3>
                  <p className="text-sm text-muted-foreground">{t('settings.themeDescription')}</p>
                </div>
                <ThemeToggle />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Language Settings */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="enhanced-card">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Globe className="w-5 h-5 text-accent-cyan" />
                <span>{t('settings.language')}</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                {languages.map((lang) => (
                  <button
                    key={lang.code}
                    onClick={() => setCurrentLanguage(lang.code)}
                    className={`p-3 rounded-lg border-2 transition-all min-h-[44px] focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 ${
                      currentLanguage === lang.code
                        ? 'border-accent-cyan bg-accent-cyan/10 text-accent-cyan'
                        : 'border-border bg-muted text-muted-foreground hover:border-muted-foreground hover:bg-muted/80'
                    }`}
                    aria-label={`Select ${lang.name} language`}
                  >
                    <div className="text-center">
                      <div className="text-xl mb-1">{lang.flag}</div>
                      <div className="text-sm font-medium">{lang.name}</div>
                    </div>
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Data Management */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="enhanced-card">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="w-5 h-5 text-accent-pink" />
                <span>{t('settings.dataManagement')}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="font-medium text-foreground mb-2">{t('settings.completeBackup')}</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  {t('settings.completeBackupDescription')}
                </p>
                <ImportExportControls dataType="all" />
              </div>

              <div className="border-t border-border pt-6">
                <h3 className="font-medium text-foreground mb-4">{t('settings.individualData')}</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <ImportExportControls dataType="swimsuits" className="h-fit" />
                  <ImportExportControls dataType="girls" className="h-fit" />
                  <ImportExportControls dataType="skills" className="h-fit" />
                  <ImportExportControls dataType="accessories" className="h-fit" />
                </div>
              </div>

              <div className="border-t border-border pt-6">
                <h3 className="font-medium text-foreground mb-4">{t('settings.calculatorData')}</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <ImportExportControls dataType="calculator" className="h-fit" />
                  <ImportExportControls dataType="venusBoard" className="h-fit" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Database Management */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="glass-effect">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="w-5 h-5 text-accent-purple" />
                <span>SQLite Database</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Database Statistics */}
              <div>
                <h3 className="font-medium text-foreground mb-3">Database Statistics</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-accent-pink/10 border border-accent-pink/30 rounded-lg p-3">
                    <div className="text-accent-pink text-sm font-medium">Swimsuits</div>
                    <div className="text-2xl font-bold text-white">{swimsuits.length}</div>
                  </div>
                  <div className="bg-accent-cyan/10 border border-accent-cyan/30 rounded-lg p-3">
                    <div className="text-accent-cyan text-sm font-medium">Girls</div>
                    <div className="text-2xl font-bold text-white">{girls.length}</div>
                  </div>
                  <div className="bg-accent-gold/10 border border-accent-gold/30 rounded-lg p-3">
                    <div className="text-accent-gold text-sm font-medium">Skills</div>
                    <div className="text-2xl font-bold text-white">{skills.length}</div>
                  </div>
                </div>
              </div>

              {/* Database Actions */}
              <div className="border-t border-border pt-6">
                <h3 className="font-medium text-foreground mb-4">Database Actions</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button
                    onClick={populateWithSampleData}
                    className="bg-accent-green hover:bg-accent-green/80 text-white"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Populate Sample Data
                  </Button>
                  
                  <Button
                    onClick={loadDataFromDatabase}
                    className="bg-accent-cyan hover:bg-accent-cyan/80 text-white"
                  >
                    <Database className="w-4 h-4 mr-2" />
                    Reload Data
                  </Button>
                  
                  <Button
                    onClick={() => {
                      if (confirm('Are you sure you want to clear all data? This cannot be undone.')) {
                        localStorage.removeItem('doaxvv-database');
                        window.location.reload();
                      }
                    }}
                    variant="destructive"
                    className="bg-red-600 hover:bg-red-700 text-white"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Clear All Data
                  </Button>

                  <Button
                    onClick={() => {
                      const data = localStorage.getItem('doaxvv-database');
                      if (data) {
                        const blob = new Blob([data], { type: 'application/json' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `doaxvv-database-${new Date().toISOString().split('T')[0]}.json`;
                        a.click();
                        URL.revokeObjectURL(url);
                      }
                    }}
                    className="bg-accent-purple hover:bg-accent-purple/80 text-white"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Export Database
                  </Button>
                </div>
                
                <div className="mt-4 p-3 bg-muted rounded-lg">
                  <p className="text-sm text-muted-foreground">
                    The database is stored locally in your browser using SQLite in WebAssembly. 
                    All data persists between sessions and is stored securely on your device.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Database Schema */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <DatabaseSchema />
        </motion.div>

        {/* About */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="glass-effect">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="w-5 h-5 text-accent-gold" />
                <span>{t('settings.about')}</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm text-muted-foreground">
                <div className="flex justify-between">
                  <span>{t('settings.version')}:</span>
                  <span className="text-foreground">1.0.0</span>
                </div>
                <div className="flex justify-between">
                  <span>{t('settings.buildDate')}:</span>
                  <span className="text-foreground">{new Date().toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>{t('settings.author')}:</span>
                  <span className="text-foreground">DOAXVV Handbook Team</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
} 